require 'rails_helper'

RSpec.describe DashboardController, type: :controller do
  let(:super_admin) { create(:user, role: :super_admin) }
  let(:regular_user) { create(:user, role: :regular_user) }
  
  before do
    # Create test data
    @brand1 = create(:brand, name: "Brand 1")
    @brand2 = create(:brand, name: "Brand 2")
    
    @store1 = create(:store, brand: @brand1)
    @store2 = create(:store, brand: @brand1)
    @store3 = create(:store, brand: @brand2)
    
    @user1 = create(:user, store: @store1)
    @user2 = create(:user, store: @store2)
    @user3 = create(:user, store: @store3)
    
    @product = create(:product)
    
    @sale1 = create(:sale, brand: @brand1, user: @user1, product: @product, status: :approved, points: 100)
    @sale2 = create(:sale, brand: @brand1, user: @user2, product: @product, status: :approved, points: 150)
    @sale3 = create(:sale, brand: @brand2, user: @user3, product: @product, status: :approved, points: 200)
  end

  describe "GET #index" do
    context "when user is super admin" do
      before { sign_in super_admin }

      it "returns http success" do
        get :index
        expect(response).to have_http_status(:success)
      end

      it "precomputes brand statistics to avoid N+1 queries" do
        # This test ensures that we don't have N+1 queries by checking
        # that the controller precomputes the data
        get :index
        
        expect(assigns(:brand_stats)).to be_present
        expect(assigns(:user_growth_stats)).to be_present
        
        # Verify brand stats structure
        brand_stats = assigns(:brand_stats)
        expect(brand_stats).to be_an(Array)
        expect(brand_stats.first).to have_key(:brand)
        expect(brand_stats.first).to have_key(:stores_count)
        expect(brand_stats.first).to have_key(:users_count)
        expect(brand_stats.first).to have_key(:monthly_sales_count)
        expect(brand_stats.first).to have_key(:approved_points)
      end

      it "computes correct brand statistics" do
        get :index
        
        brand_stats = assigns(:brand_stats)
        brand1_stats = brand_stats.find { |stat| stat[:brand].id == @brand1.id }
        brand2_stats = brand_stats.find { |stat| stat[:brand].id == @brand2.id }
        
        expect(brand1_stats[:stores_count]).to eq(2)
        expect(brand1_stats[:users_count]).to eq(2)
        expect(brand1_stats[:approved_points]).to eq(250) # 100 + 150
        
        expect(brand2_stats[:stores_count]).to eq(1)
        expect(brand2_stats[:users_count]).to eq(1)
        expect(brand2_stats[:approved_points]).to eq(200)
      end

      it "computes user growth statistics" do
        get :index
        
        user_growth_stats = assigns(:user_growth_stats)
        expect(user_growth_stats).to be_an(Array)
        expect(user_growth_stats.length).to eq(6) # 6 months
        
        # Check structure
        expect(user_growth_stats.first).to have_key(:month_start)
        expect(user_growth_stats.first).to have_key(:month_name)
        expect(user_growth_stats.first).to have_key(:user_count)
      end

      it "executes minimal database queries" do
        # This test ensures we're not making excessive queries
        expect do
          get :index
        end.to make_database_queries(count: be < 20) # Reasonable upper limit
      end
    end

    context "when user is not super admin" do
      before { sign_in regular_user }

      it "returns http success" do
        get :index
        expect(response).to have_http_status(:success)
      end

      it "does not precompute super admin data" do
        get :index
        
        expect(assigns(:brand_stats)).to be_nil
        expect(assigns(:user_growth_stats)).to be_nil
      end
    end
  end

  describe "private methods" do
    let(:controller) { described_class.new }
    
    before do
      allow(controller).to receive(:current_user).and_return(super_admin)
    end

    describe "#compute_brand_statistics" do
      it "returns array of brand statistics" do
        stats = controller.send(:compute_brand_statistics)
        
        expect(stats).to be_an(Array)
        expect(stats.length).to eq(Brand.count)
        
        brand1_stat = stats.find { |stat| stat[:brand].id == @brand1.id }
        expect(brand1_stat[:stores_count]).to eq(2)
        expect(brand1_stat[:users_count]).to eq(2)
        expect(brand1_stat[:approved_points]).to eq(250)
      end
    end

    describe "#compute_user_growth_statistics" do
      it "returns array of user growth statistics for 6 months" do
        stats = controller.send(:compute_user_growth_statistics)
        
        expect(stats).to be_an(Array)
        expect(stats.length).to eq(6)
        
        stats.each do |stat|
          expect(stat).to have_key(:month_start)
          expect(stat).to have_key(:month_name)
          expect(stat).to have_key(:user_count)
          expect(stat[:user_count]).to be_a(Integer)
        end
      end
    end
  end
end
