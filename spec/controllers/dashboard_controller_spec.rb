require "rails_helper"

RSpec.describe DashboardController, type: :controller do
  # Skip meilisearch indexing for these tests
  before(:all) do
    # Stub all meilisearch methods to prevent indexing issues
    allow_any_instance_of(User).to receive(:ms_index!).and_return(true)
    allow_any_instance_of(Sale).to receive(:ms_index!).and_return(true)
    allow_any_instance_of(Store).to receive(:ms_index!).and_return(true)
    allow_any_instance_of(Product).to receive(:ms_index!).and_return(true)
    allow_any_instance_of(User).to receive(:ms_remove_from_index!).and_return(true)
    allow_any_instance_of(Sale).to receive(:ms_remove_from_index!).and_return(true)
    allow_any_instance_of(Store).to receive(:ms_remove_from_index!).and_return(true)
    allow_any_instance_of(Product).to receive(:ms_remove_from_index!).and_return(true)
  end

  describe "Dashboard N+1 Query Fix" do
    let(:super_admin) { User.create!(email: "<EMAIL>", password: "password", first_name: "Admin", last_name: "User", role: :super_admin, phone_number: "1234567890") }
    let(:regular_user) { User.create!(email: "<EMAIL>", password: "password", first_name: "Regular", last_name: "User", role: :regular_user, phone_number: "1234567891") }

    before do
      # Create minimal test data without complex relationships
      @brand1 = Brand.create!(name: "Brand 1")
      @brand2 = Brand.create!(name: "Brand 2")

      # Create stores directly without addresses to avoid meilisearch issues
      @store1 = Store.create!(name: "Store 1", brand: @brand1)
      @store2 = Store.create!(name: "Store 2", brand: @brand1)
      @store3 = Store.create!(name: "Store 3", brand: @brand2)

      # Create users with stores
      @user1 = User.create!(email: "<EMAIL>", password: "password", first_name: "User", last_name: "One", store: @store1, phone_number: "1111111111")
      @user2 = User.create!(email: "<EMAIL>", password: "password", first_name: "User", last_name: "Two", store: @store2, phone_number: "2222222222")
      @user3 = User.create!(email: "<EMAIL>", password: "password", first_name: "User", last_name: "Three", store: @store3, phone_number: "3333333333")

      # Create regions
      @region1 = Region.create!(name: "Region 1")
      @region2 = Region.create!(name: "Region 2")

      # Create categories
      @category1 = Category.create!(name: "Category 1", brand: @brand1)
      @category2 = Category.create!(name: "Category 2", brand: @brand2)

      # Create products
      @product1 = Product.create!(name: "Product 1", category: @category1, sku: "SKU1", upc: "123456789012")
      @product2 = Product.create!(name: "Product 2", category: @category2, sku: "SKU2", upc: "123456789013")

      # Create sales with explicit points to avoid assign_points callback issues
      @sale1 = Sale.new(brand: @brand1, region: @region1, user: @user1, product: @product1, status: :approved, points: 100, sold_at: Time.current, serial_number: "1234567890")
      @sale1.save!(validate: false) # Skip validations to avoid meilisearch issues

      @sale2 = Sale.new(brand: @brand1, region: @region1, user: @user2, product: @product1, status: :approved, points: 150, sold_at: Time.current, serial_number: "1234567891")
      @sale2.save!(validate: false)

      @sale3 = Sale.new(brand: @brand2, region: @region2, user: @user3, product: @product2, status: :approved, points: 200, sold_at: Time.current, serial_number: "1234567892")
      @sale3.save!(validate: false)
    end

    describe "GET #index" do
      context "when user is super admin" do
        before { sign_in super_admin }

        it "returns http success" do
          get :index
          expect(response).to have_http_status(:success)
        end

        it "precomputes brand statistics to avoid N+1 queries" do
          # This test ensures that we don't have N+1 queries by checking
          # that the controller precomputes the data
          get :index

          expect(assigns(:brand_stats)).to be_present
          expect(assigns(:user_growth_stats)).to be_present

          # Verify brand stats structure
          brand_stats = assigns(:brand_stats)
          expect(brand_stats).to be_an(Array)
          expect(brand_stats.first).to have_key(:brand)
          expect(brand_stats.first).to have_key(:stores_count)
          expect(brand_stats.first).to have_key(:users_count)
          expect(brand_stats.first).to have_key(:monthly_sales_count)
          expect(brand_stats.first).to have_key(:approved_points)
        end

        it "computes correct brand statistics" do
          get :index

          brand_stats = assigns(:brand_stats)
          brand1_stats = brand_stats.find { |stat| stat[:brand].id == @brand1.id }
          brand2_stats = brand_stats.find { |stat| stat[:brand].id == @brand2.id }

          expect(brand1_stats[:stores_count]).to eq(2)
          expect(brand1_stats[:users_count]).to eq(2)
          expect(brand1_stats[:approved_points]).to eq(250) # 100 + 150

          expect(brand2_stats[:stores_count]).to eq(1)
          expect(brand2_stats[:users_count]).to eq(1)
          expect(brand2_stats[:approved_points]).to eq(200)
        end

        it "computes user growth statistics" do
          get :index

          user_growth_stats = assigns(:user_growth_stats)
          expect(user_growth_stats).to be_an(Array)
          expect(user_growth_stats.length).to eq(6) # 6 months

          # Check structure
          expect(user_growth_stats.first).to have_key(:month_start)
          expect(user_growth_stats.first).to have_key(:month_name)
          expect(user_growth_stats.first).to have_key(:user_count)
        end

        it "executes minimal database queries" do
          # This test ensures we're not making excessive queries
          expect do
            get :index
          end.to make_database_queries(count: be < 20) # Reasonable upper limit
        end
      end

      context "when user is not super admin" do
        before { sign_in regular_user }

        it "returns http success" do
          get :index
          expect(response).to have_http_status(:success)
        end

        it "does not precompute super admin data" do
          get :index

          expect(assigns(:brand_stats)).to be_nil
          expect(assigns(:user_growth_stats)).to be_nil
        end
      end
    end

    describe "private methods" do
      let(:controller) { described_class.new }

      before do
        allow(controller).to receive(:current_user).and_return(super_admin)
      end

      describe "#compute_brand_statistics" do
        it "returns array of brand statistics" do
          stats = controller.send(:compute_brand_statistics)

          expect(stats).to be_an(Array)
          expect(stats.length).to eq(Brand.count)

          brand1_stat = stats.find { |stat| stat[:brand].id == @brand1.id }
          expect(brand1_stat[:stores_count]).to eq(2)
          expect(brand1_stat[:users_count]).to eq(2)
          expect(brand1_stat[:approved_points]).to eq(250)
        end
      end

      describe "#compute_user_growth_statistics" do
        it "returns array of user growth statistics for 6 months" do
          stats = controller.send(:compute_user_growth_statistics)

          expect(stats).to be_an(Array)
          expect(stats.length).to eq(6)

          stats.each do |stat|
            expect(stat).to have_key(:month_start)
            expect(stat).to have_key(:month_name)
            expect(stat).to have_key(:user_count)
            expect(stat[:user_count]).to be_a(Integer)
          end
        end
      end
    end
  end
end
