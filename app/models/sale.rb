# == Schema Information
#
# Table name: sales
#
#  id            :bigint           not null, primary key
#  approved_at   :datetime
#  notes         :text
#  origin        :integer          default("internet")
#  points        :integer
#  serial_number :string
#  sold_at       :datetime
#  status        :integer          default("pending")
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  admin_id      :bigint
#  ahoy_visit_id :bigint
#  brand_id      :bigint
#  product_id    :bigint           not null
#  promotion_id  :bigint
#  region_id     :bigint
#  user_id       :bigint           not null
#
# Indexes
#
#  index_sales_on_admin_id                             (admin_id)
#  index_sales_on_brand_id                             (brand_id)
#  index_sales_on_brand_id_and_status_and_created_at   (brand_id,status,created_at)
#  index_sales_on_created_at_and_status                (created_at,status)
#  index_sales_on_product_id                           (product_id)
#  index_sales_on_promotion_id                         (promotion_id)
#  index_sales_on_region_id                            (region_id)
#  index_sales_on_region_id_and_status_and_created_at  (region_id,status,created_at)
#  index_sales_on_sold_at_and_status                   (sold_at,status)
#  index_sales_on_status_and_created_at                (status,created_at)
#  index_sales_on_user_id                              (user_id)
#  index_sales_on_user_id_and_status_and_created_at    (user_id,status,created_at)
#
class Sale < ApplicationRecord
  FILTER_FIELDS = %w[brand_name status sold_at origin country_name].freeze

  include MeiliSearch::Rails
  extend Pagy::Meilisearch
  ActiveRecord_Relation.include Pagy::Meilisearch
  include Flipper::Identifier

  before_save :assign_points

  after_create_commit :notify_admins
  after_create_commit :record_activity

  before_update :update_status

  after_update_commit :notify_user
  after_update :log_status_changes, if: :saved_change_to_status?
  after_update :log_points_changes, if: :saved_change_to_points?

  enum :status, pending: 0, approved: 1, declined: 2
  enum :origin, internet: 0, in_store: 1

  belongs_to :user, -> { includes :store }, counter_cache: true
  belongs_to :product, counter_cache: true
  belongs_to :admin, class_name: "User", optional: true
  belongs_to :brand, counter_cache: true
  belongs_to :region, counter_cache: true
  belongs_to :promotion, optional: true

  has_one :activity, as: :transactable, dependent: :destroy

  has_many :notations, as: :noteable, dependent: :destroy, class_name: "Note"

  # Conditional counter caches
  counter_culture :user, column_name: proc { |model| model.approved? ? "approved_sales_count" : nil }
  counter_culture :user, column_name: proc { |model| model.pending? ? "pending_sales_count" : nil }
  counter_culture :brand, column_name: proc { |model| model.approved? ? "approved_sales_count" : nil }

  validates :serial_number, :status, :sold_at, presence: true
  validates :serial_number, uniqueness: {case_sensitive: false}, numericality: {only_integer: true}, list: true, length: {minimum: 7}
  validates :sold_at, timeliness: {type: :date, on_or_before: :today, on_or_after: 1.year.ago}
  validates :points, numericality: {greater_than_or_equal_to: 0}, allow_nil: true

  # Business rule validations
  validate :product_available_in_region
  validate :promotion_valid_for_product, if: :promotion_id?
  validate :sale_date_not_in_future
  validate :serial_number_format
  validate :user_can_sell_product

  visitable :ahoy_visit

  has_paper_trail

  delegate :name, to: :product, prefix: true, allow_nil: true
  delegate :name, to: :user, prefix: true
  delegate :name, to: :admin, prefix: true, allow_nil: true
  delegate :name, to: :brand, prefix: true

  accepts_nested_attributes_for :notations

  has_one_attached :receipt
  validates :receipt, presence: true, if: -> { Current.country == "CA" }

  scope :meilisearch_import, -> { includes(:brand, :product, user: [address: :country]) }

  # Common query scopes
  scope :recent, -> { where(created_at: 1.month.ago..) }
  scope :by_status, ->(status) { where(status: status) }
  scope :by_date_range, ->(start_date, end_date) { where(sold_at: start_date..end_date) }
  scope :with_promotions, -> { where.not(promotion_id: nil) }
  scope :without_promotions, -> { where(promotion_id: nil) }
  scope :for_reporting, -> { includes(:user, :product, :brand, :region, :promotion) }
  scope :by_origin, ->(origin) { where(origin: origin) }
  scope :approved, -> { where(status: :approved) }
  scope :pending, -> { where(status: :pending) }
  scope :declined, -> { where(status: :declined) }
  scope :this_month, -> { where(created_at: Date.current.beginning_of_month..Date.current.end_of_month) }
  scope :last_month, -> { where(created_at: 1.month.ago.beginning_of_month..1.month.ago.end_of_month) }
  scope :by_brand, ->(brand_id) { where(brand_id: brand_id) }
  scope :by_region, ->(region_id) { where(region_id: region_id) }
  scope :by_user, ->(user_id) { where(user_id: user_id) }
  scope :high_value, -> { joins(:product).where("products.points_earned > ?", 100) }
  scope :with_receipts, -> { joins(:receipt_attachment) }

  def name
    "Sale on #{sold_at} by #{user.name}"
  end

  # Calculated fields and business logic methods
  def total_value
    return 0 unless product && user&.address&.country
    product.msrp_for_country(user.address.country.id)
  end

  def commission_earned
    return 0 unless approved? && total_value > 0
    base_commission = total_value * 0.05 # 5% base commission
    promotion_multiplier = promotion&.multiplier || 1.0
    (base_commission * promotion_multiplier).round(2)
  end

  def processing_time_days
    return nil unless approved_at && created_at
    ((approved_at - created_at) / 1.day).round(1)
  end

  def points_multiplier
    promotion&.multiplier || 1.0
  end

  def base_points_earned
    return 0 unless product && user&.address&.country
    product.points_earned_for_country(user.address.country.id)
  end

  def effective_points_earned
    (base_points_earned * points_multiplier).round
  end

  def is_high_value?
    total_value > 1000
  end

  def days_since_sale
    return 0 unless sold_at
    ((Date.current - sold_at.to_date)).to_i
  end

  def status_display
    case status
    when "pending"
      "Awaiting Approval"
    when "approved"
      "Approved"
    when "declined"
      "Declined"
    else
      status.humanize
    end
  end

  # Class methods for reporting and analytics
  def self.monthly_summary(month = Date.current.beginning_of_month)
    where(created_at: month..month.end_of_month)
      .group(:status)
      .group_by_day(:created_at)
      .sum(:points)
  end

  def self.top_performers(limit = 10)
    results = joins(:user)
      .where(status: :approved)
      .group("users.id", "users.first_name", "users.last_name")
      .sum(:points)
      .sort_by { |_, points| -points }
      .first(limit)

    results.map { |user_data, points| {user: user_data, points: points} }
  end

  def self.brand_performance(start_date = 1.month.ago, end_date = Date.current)
    joins(:brand)
      .where(created_at: start_date..end_date)
      .group("brands.name")
      .group(:status)
      .count
  end

  def self.regional_breakdown(start_date = 1.month.ago, end_date = Date.current)
    joins(:region)
      .where(created_at: start_date..end_date, status: :approved)
      .group("regions.name")
      .sum(:points)
  end

  def self.promotion_effectiveness
    joins(:promotion)
      .where(status: :approved)
      .group("promotions.name")
      .average("sales.points / promotions.multiplier")
  end

  def self.conversion_rates(start_date = 1.month.ago, end_date = Date.current)
    sales_in_period = where(created_at: start_date..end_date)
    total_count = sales_in_period.count

    return {} if total_count.zero?

    {
      total_sales: total_count,
      approved_rate: (sales_in_period.approved.count.to_f / total_count * 100).round(2),
      declined_rate: (sales_in_period.declined.count.to_f / total_count * 100).round(2),
      pending_rate: (sales_in_period.pending.count.to_f / total_count * 100).round(2)
    }
  end

  def self.average_processing_time
    approved.where.not(approved_at: nil)
      .average("EXTRACT(EPOCH FROM (approved_at - created_at)) / 86400")
      &.round(2)
  end

  meilisearch do
    attribute :status, :origin, :serial_number, :brand_name, :product_name, :user_id, :brand_id, :region_id

    attribute :sold_at do
      sold_at.to_time.to_i
    end

    attribute :created_at do
      created_at.to_time.to_i
    end

    attribute :country_name do
      user.address.country.name
    end

    filterable_attributes [:brand_name, :status, :sold_at, :origin, :user_id, :brand_id, :region_id, :country_name]
    sortable_attributes [:sold_at, :created_at]
  end

  private

  def assign_points
    self.points = promotion.present? ? (promotion.multiplier * product.points_earned_for_country(Current.country.id)) : product.points_earned_for_country(Current.country.id)
  end

  # Custom validation methods
  def product_available_in_region
    return unless product && region
    unless product.category.brand == region.brand
      errors.add(:product, "is not available in this region")
    end
  end

  def promotion_valid_for_product
    return unless promotion && product
    unless promotion.products.include?(product)
      errors.add(:promotion, "is not valid for this product")
    end

    # Check if promotion is active
    if promotion.starts_at && promotion.starts_at > sold_at
      errors.add(:promotion, "was not active on the sale date")
    end

    if promotion.ends_at && promotion.ends_at < sold_at
      errors.add(:promotion, "had expired on the sale date")
    end
  end

  def sale_date_not_in_future
    return unless sold_at
    if sold_at > Time.current
      errors.add(:sold_at, "cannot be in the future")
    end
  end

  def serial_number_format
    return unless serial_number
    unless serial_number.match?(/\A[0-9]{7,}\z/)
      errors.add(:serial_number, "must be numeric and at least 7 digits")
    end
  end

  def user_can_sell_product
    return unless user && product
    unless user.active_status?
      errors.add(:user, "must be active to create sales")
    end

    # Check if user's store brand matches product brand
    if user.store && product.category.brand != user.store.brand
      errors.add(:product, "brand does not match your store's brand")
    end
  end

  # Audit logging methods
  def log_status_changes
    old_status = status_before_last_save
    new_status = status
    current_user_email = Current.user&.email || "system"

    Rails.logger.info "Sale #{id} status changed from #{old_status} to #{new_status} by #{current_user_email}"

    # Log to audit trail if needed
    if defined?(AuditLog)
      AuditLog.create(
        auditable: self,
        action: "status_change",
        old_value: old_status,
        new_value: new_status,
        user_email: current_user_email,
        metadata: {
          points: points,
          product_id: product_id,
          user_id: user_id
        }
      )
    end
  end

  def log_points_changes
    old_points = points_before_last_save
    new_points = points
    current_user_email = Current.user&.email || "system"

    Rails.logger.info "Sale #{id} points changed from #{old_points} to #{new_points} by #{current_user_email}"

    # Alert on significant point changes
    if old_points && (new_points - old_points).abs > 100
      Rails.logger.warn "Significant point change detected for Sale #{id}: #{old_points} -> #{new_points}"
    end
  end

  def notify_admins
    NewSaleNotifier.with(sale: self).deliver_later(User.admins)
  end

  def record_activity
    create_activity(wallet: user.wallet, kind: "credit", transactable: self)
  end

  def notify_user
    if status_changed?
      UpdatedSaleNotifier.with(sale: self).deliver_later(user)
    end
  end

  def update_status
    if status_changed?
      self.approved_at = Time.zone.now
      case status
      when "approved"
        activity.approved!
        user.wallet.credit!(points)
      when "declined"
        activity.declined!
      end
    end
  end
end
